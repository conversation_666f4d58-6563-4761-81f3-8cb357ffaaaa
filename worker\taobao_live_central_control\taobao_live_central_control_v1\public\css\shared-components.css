/* 共享样式文件 - 包含提取订单和页面筛选相关样式 */

/* 快捷选择按钮样式 */
.quick-select-btn {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    background-color: #ffffff;
    color: #374151;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quick-select-btn:hover {
    border-color: #10b981;
    background-color: #ecfdf5;
    color: #047857;
}

.quick-select-btn.active {
    border-color: #10b981;
    background-color: #10b981;
    color: #ffffff;
}

/* 订单提取主播按钮样式 */
.order-anchor-btn {
    padding: 6px 12px;
    border: 1px solid #d1d5db;
    background-color: #ffffff;
    color: #374151;
    border-radius: 16px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.order-anchor-btn:hover {
    border-color: #ea580c;
    background-color: #fff7ed;
    color: #ea580c;
}

.order-anchor-btn-active {
    border-color: #ea580c;
    background-color: #ea580c;
    color: #ffffff;
}

.order-anchor-btn-active:hover {
    border-color: #dc2626;
    background-color: #dc2626;
    color: #ffffff;
}

/* 页面筛选主播按钮样式 */
.anchor-btn {
    padding: 6px 16px;
    border: 2px solid #d1d5db;
    background-color: #ffffff;
    color: #374151;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.anchor-btn:hover {
    border-color: #ea580c;
    color: #ea580c;
    background-color: #fff7ed;
}

.anchor-btn-active {
    border-color: #ea580c;
    background-color: #ea580c;
    color: #ffffff;
}

.anchor-btn-active:hover {
    border-color: #dc2626;
    background-color: #dc2626;
    color: #ffffff;
}

/* 时间按钮样式 */
.time-btn {
    padding: 6px 16px;
    border: 2px solid #d1d5db;
    background-color: #ffffff;
    color: #374151;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.time-btn:hover {
    border-color: #4f46e5;
    color: #4f46e5;
    background-color: #eef2ff;
}

.time-btn-active {
    border-color: #4f46e5;
    background-color: #4f46e5;
    color: #ffffff;
}

.time-btn-active:hover {
    border-color: #3730a3;
    background-color: #3730a3;
    color: #ffffff;
}
