/**
 * Modal组件加载器
 * 用于动态加载共享的modal组件，避免重复代码
 */

/**
 * 加载提取订单modal组件
 * @returns {Promise<void>}
 */
async function loadExtractOrdersModal() {
    try {
        // 检查是否已经加载过
        if (document.getElementById('extractOrdersModal')) {
            return;
        }

        // 加载modal HTML
        const response = await fetch('components/extract-orders-modal.html');
        if (!response.ok) {
            throw new Error('Failed to load extract orders modal');
        }

        const modalHtml = await response.text();

        // 创建临时容器并插入HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = modalHtml;

        // 将modal添加到body
        document.body.appendChild(tempDiv.firstElementChild);

        // modal加载完成后，绑定提取订单相关事件
        if (typeof bindExtractOrdersModalEvents === 'function') {
            bindExtractOrdersModalEvents();
            console.log('Extract orders modal events bound successfully');
        } else {
            console.warn('bindExtractOrdersModalEvents function not found');
        }

        console.log('Extract orders modal loaded successfully');
    } catch (error) {
        console.error('Error loading extract orders modal:', error);
        // 如果加载失败，可以考虑显示错误信息或使用备用方案
    }
}

/**
 * 初始化页面时加载所需的modal组件
 */
async function initializeModals() {
    // 根据页面需要加载相应的modal
    const currentPage = window.location.pathname;
    
    if (currentPage.includes('live-orders.html') || currentPage.includes('live-plans.html')) {
        await loadExtractOrdersModal();
    }
}

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeModals();
});

// 将函数添加到全局作用域
window.loadExtractOrdersModal = loadExtractOrdersModal;
window.initializeModals = initializeModals;

// 导出函数供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        loadExtractOrdersModal,
        initializeModals
    };
}
