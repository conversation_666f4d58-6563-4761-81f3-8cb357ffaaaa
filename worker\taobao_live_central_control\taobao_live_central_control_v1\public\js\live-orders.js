// 当前页码和每页显示数量
let currentPage = 1;
let pageSize = 10;
let totalPages = 0;
let filters = {};
let currentSortField = 'pay_time';
let currentSortOrder = 'DESC';
let searchTimeout = null;

// 页面加载完成后获取数据
$(document).ready(function () {
    if (typeof layer === 'undefined') {
        console.error('layer.js 未正确加载');
        return;
    }

    initEventListeners();

    // 检查是否有访问密码，如果没有则显示输入弹窗
    const apiKey = getCookie('api_key');
    if (!apiKey) {
        showApiKeyModal();
        return;
    }

    // 设置默认排序图标
    $(`#sort-${currentSortField}`).addClass(currentSortOrder === 'ASC' ? 'sort-asc' : 'sort-desc');

    // 绑定按钮点击事件
    bindAnchorButtonEvents(handleAnchorOrTimeChange);
    bindTimeButtonEvents(handleAnchorOrTimeChange);

    // 提取订单modal的事件将在modal加载完成后自动绑定

    // 并行加载数据以提高性能
    Promise.all([
        loadAnchorNames(),
        loadOrdersAndStats()
    ]).catch(error => {
        console.error('初始化数据加载失败:', error);
    });
});

// 初始化事件监听器
function initEventListeners() {
    // 筛选表单提交
    $('#filterForm').on('submit', function (e) {
        e.preventDefault();
        currentPage = 1;
        loadOrders();
    });

    // 重置按钮
    $('#resetBtn').on('click', function () {
        $('#filterForm')[0].reset();

        // 重置主播按钮状态
        $('.anchor-btn').removeClass('anchor-btn-active');
        $('.anchor-btn[data-anchor=""]').addClass('anchor-btn-active');
        $('#anchorFilter').val('');

        // 重置时间按钮状态
        $('.time-btn').removeClass('time-btn-active');
        $('.time-btn[data-days=""]').addClass('time-btn-active');
        $('#timeFilter').val('');

        currentPage = 1;
        loadOrders();
    });

    // 导出按钮
    $('#exportBtn').on('click', function () {
        exportOrders();
    });

    // 提取订单按钮
    $('#extractOrdersBtn').on('click', function () {
        showExtractOrdersModal();
    });

    // 移动端分页按钮
    $('#prevPageMobile').on('click', function () {
        if (currentPage > 1) {
            currentPage--;
            loadOrders();
        }
    });

    $('#nextPageMobile').on('click', function () {
        if (currentPage < totalPages) {
            currentPage++;
            loadOrders();
        }
    });

    // 访问密码提交按钮
    $('#submitApiKeyBtn').on('click', function () {
        submitApiKey();
    });

    // 访问密码表单回车提交
    $('#apiKeyForm').on('submit', function (e) {
        e.preventDefault();
        submitApiKey();
    });

    // 搜索输入框防抖
    $('#itemSearch, #liveIdFilter, #minAmount, #maxAmount').on('input', function () {
        debouncedSearch();
    });

    $('#startDate, #endDate').on('change', function () {
        debouncedSearch();
    });
}

// 防抖查询函数
function debouncedSearch() {
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }
    searchTimeout = setTimeout(function () {
        currentPage = 1;
        loadOrders();
    }, 300);
}

// 加载主播名称列表
async function loadAnchorNames() {
    try {
        const response = await fetch("/api/anchors?mode=full", {
            headers: addApiKeyHeader()
        });

        if (response.status === 401) {
            const data = await response.json();
            if (data.error === "invalid_api_key") {
                showApiKeyModal();
                return;
            }
        }

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || '获取主播列表失败');
        }

        if (data.anchors && data.anchors.length > 0) {
            // 按sort字段正序排列主播
            const sortedAnchors = [...data.anchors].sort((a, b) => {
                const sortA = parseInt(a.sort) || 0;
                const sortB = parseInt(b.sort) || 0;
                return sortA - sortB;
            });

            // 生成主播按钮
            const anchorButtonsContainer = $('#anchorButtons');

            // 清空容器，但保留"全部"按钮
            anchorButtonsContainer.find('.anchor-btn:not([data-anchor=""])').remove();

            // 添加主播按钮
            sortedAnchors.forEach(anchor => {
                const button = $(`
                    <button type="button" class="anchor-btn" data-anchor="${anchor.anchor_name}">
                        ${anchor.anchor_name}
                    </button>
                `);
                anchorButtonsContainer.append(button);
            });

            // 绑定按钮点击事件
            bindAnchorButtonEvents(handleAnchorOrTimeChange);
        }
    } catch (error) {
        console.error('加载主播列表失败:', error);
        layer.msg('加载主播列表失败: ' + error.message, { icon: 2 });
    }
}

// 主播按钮和时间按钮事件处理已移至 shared-components.js
// 这里保留一个包装函数来处理页面特定的逻辑
function handleAnchorOrTimeChange() {
    currentPage = 1;
    loadOrders();
}

// 加载订单数据和统计信息
async function loadOrdersAndStats() {
    try {
        await Promise.all([
            loadOrders(),
            loadOrdersStats()
        ]);
    } catch (error) {
        console.error('加载数据失败:', error);
        layer.msg('加载数据失败: ' + error.message, { icon: 2 });
    }
}

// 仅加载订单数据
async function loadOrders() {
    try {
        showLoadingState();

        const formData = getFilterParams();
        const params = new URLSearchParams(formData);

        params.append('page', currentPage);
        params.append('limit', pageSize);
        params.append('sortField', currentSortField);
        params.append('sortOrder', currentSortOrder);

        const response = await fetch(`/api/orders?${params.toString()}`, {
            headers: addApiKeyHeader()
        });

        if (response.status === 401) {
            const data = await response.json();
            if (data.error === "invalid_api_key") {
                showApiKeyModal();
                return;
            }
        }

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || '获取订单数据失败');
        }

        updateOrdersTable(data.orders || []);
        updatePagination(data.pagination || {});
        updateTotalCount(data.pagination?.total || 0);

    } catch (error) {
        console.error('加载订单数据失败:', error);
        showErrorState('加载数据失败: ' + error.message);
    }
}

// 加载统计信息
async function loadOrdersStats() {
    try {
        const formData = getFilterParams();
        const params = new URLSearchParams(formData);

        const response = await fetch(`/api/orders/stats?${params.toString()}`, {
            headers: addApiKeyHeader()
        });

        if (response.status === 401) {
            const data = await response.json();
            if (data.error === "invalid_api_key") {
                showApiKeyModal();
                return;
            }
        }

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || '获取统计数据失败');
        }

        updateStatsCards(data.stats || {});

    } catch (error) {
        console.error('加载统计数据失败:', error);
    }
}

// 获取筛选参数
function getFilterParams() {
    const formData = new FormData(document.getElementById('filterForm'));
    const params = {};

    for (let [key, value] of formData.entries()) {
        if (value && value.trim() !== '') {
            params[key] = value.trim();
        }
    }

    return params;
}

// 更新订单表格
function updateOrdersTable(orders) {
    const tbody = $('#ordersTable');
    tbody.empty();

    if (!orders || orders.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="13" class="px-6 py-4 text-center text-sm text-gray-500">暂无数据</td>
            </tr>
        `);
        return;
    }

    orders.forEach(order => {
        const netAmount = (order.pay_amount || 0) - (order.refund_amount || 0);

        tbody.append(`
            <tr class="hover:bg-gray-50">
                <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">${order.child_order_id || '--'}</td>
                <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">${order.anchor_name || '--'}</td>
                <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">${order.live_id || '--'}</td>
                <td class="px-3 py-4 text-sm text-center text-gray-900 max-w-xs truncate" title="${order.item_title || ''}">${order.item_title || '--'}</td>
                <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">${order.item_id || '--'}</td>
                <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">${formatDateTime(order.pay_time)}</td>
                <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">${formatDateTime(order.live_start_time)}</td>
                <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">${order.pay_days >= 0 ? order.pay_days + '天' : '--'}</td>
                <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">¥${formatNumber(order.pay_amount || 0)}</td>
                <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">¥${formatNumber(order.refund_amount || 0)}</td>
                <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">${order.quantity || 1}</td>
                <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">¥${formatNumber(netAmount)}</td>
                <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">${formatDateTime(order.created_at)}</td>
            </tr>
        `);
    });
}

// 更新统计卡片
function updateStatsCards(stats) {
    $('#totalOrders').text(formatNumber(stats.totalOrders || 0));
    $('#totalSales').text('¥' + formatNumber(stats.totalSales || 0));
    $('#avgOrderValue').text('¥' + formatNumber(stats.avgOrderValue || 0));
    $('#totalRefunds').text('¥' + formatNumber(stats.totalRefunds || 0));
}

// 显示加载状态
function showLoadingState() {
    $('#ordersTable').html(`
        <tr>
            <td colspan="13" class="px-6 py-4 text-center text-sm text-gray-500">
                <i class="fas fa-spinner fa-spin mr-2"></i>加载中...
            </td>
        </tr>
    `);
}

// 显示错误状态
function showErrorState(message) {
    $('#ordersTable').html(`
        <tr>
            <td colspan="13" class="px-6 py-4 text-center text-sm text-red-500">
                <i class="fas fa-exclamation-triangle mr-2"></i>${message}
            </td>
        </tr>
    `);
}

// 更新总数显示
function updateTotalCount(total) {
    $('#totalCount').text(formatNumber(total));
}

// 排序功能
function toggleSort(field) {
    if (currentSortField === field) {
        currentSortOrder = currentSortOrder === 'ASC' ? 'DESC' : 'ASC';
    } else {
        currentSortField = field;
        currentSortOrder = 'DESC';
    }

    // 更新排序图标
    $('.sort-icon').removeClass('sort-asc sort-desc');
    $(`#sort-${field}`).addClass(currentSortOrder === 'ASC' ? 'sort-asc' : 'sort-desc');

    // 重新加载数据
    currentPage = 1;
    loadOrders();
}

// 更新分页
function updatePagination(pagination) {
    totalPages = pagination.totalPages || 1;
    const total = pagination.total || 0;
    const page = pagination.page || 1;
    const limit = pagination.limit || 10;

    // 更新移动端分页
    $('#currentPageMobile').text(page);
    $('#totalPagesMobile').text(totalPages);

    // 更新桌面端分页信息
    const startItem = total === 0 ? 0 : (page - 1) * limit + 1;
    const endItem = Math.min(page * limit, total);

    $('#startItem').text(startItem);
    $('#endItem').text(endItem);
    $('#totalItems').text(total);

    // 更新分页按钮状态
    $('#prevPageMobile').prop('disabled', page <= 1);
    $('#nextPageMobile').prop('disabled', page >= totalPages);

    // 生成桌面端分页按钮
    generatePaginationButtons(page, totalPages);
}

// 生成分页按钮
function generatePaginationButtons(currentPage, totalPages) {
    const pagination = $('#pagination');
    pagination.empty();

    if (totalPages <= 1) return;

    // 上一页按钮
    const prevDisabled = currentPage <= 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50';
    pagination.append(`
        <button onclick="${currentPage > 1 ? 'changePage(' + (currentPage - 1) + ')' : ''}"
                class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 ${prevDisabled}">
            <i class="fas fa-chevron-left"></i>
        </button>
    `);

    // 页码按钮
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    if (startPage > 1) {
        pagination.append(`
            <button onclick="changePage(1)" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">1</button>
        `);
        if (startPage > 2) {
            pagination.append(`<span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>`);
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        const isActive = i === currentPage;
        const buttonClass = isActive
            ? 'relative inline-flex items-center px-4 py-2 border border-orange-500 bg-orange-50 text-sm font-medium text-orange-600'
            : 'relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50';

        pagination.append(`
            <button onclick="${isActive ? '' : 'changePage(' + i + ')'}" class="${buttonClass}">${i}</button>
        `);
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            pagination.append(`<span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>`);
        }
        pagination.append(`
            <button onclick="changePage(${totalPages})" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">${totalPages}</button>
        `);
    }

    // 下一页按钮
    const nextDisabled = currentPage >= totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50';
    pagination.append(`
        <button onclick="${currentPage < totalPages ? 'changePage(' + (currentPage + 1) + ')' : ''}"
                class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 ${nextDisabled}">
            <i class="fas fa-chevron-right"></i>
        </button>
    `);
}

// 切换页面
function changePage(page) {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
        currentPage = page;
        loadOrders();
    }
}

// 导出订单数据
async function exportOrders() {
    try {
        const formData = getFilterParams();
        const params = new URLSearchParams(formData);

        // 显示加载状态
        const loadingIndex = layer.msg('正在导出数据...', {
            icon: 16,
            shade: 0.3,
            time: 0
        });

        const response = await fetch(`/api/orders/export?${params.toString()}`, {
            headers: addApiKeyHeader()
        });

        layer.close(loadingIndex);

        if (response.status === 401) {
            const data = await response.json();
            if (data.error === "invalid_api_key") {
                showApiKeyModal();
                return;
            }
        }

        const result = await response.json();

        if (!response.ok) {
            throw new Error(result.error || '导出失败');
        }

        if (result.success && result.data && result.data.length > 0) {
            // 使用 SheetJS 生成 Excel 文件
            const ws = XLSX.utils.json_to_sheet(result.data);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "订单数据");
            
            // 生成文件名
            const fileName = `直播订单数据_${formatDate(new Date())}.xlsx`;
            
            // 下载文件
            XLSX.writeFile(wb, fileName);
            
            layer.msg(`成功导出 ${result.total} 条订单数据`, { icon: 1 });
        } else {
            layer.msg('没有可导出的数据', { icon: 0 });
        }

    } catch (error) {
        console.error('导出订单数据失败:', error);
        layer.msg('导出失败: ' + error.message, { icon: 2 });
    }
}

// 工具函数
function formatNumber(num) {
    if (num === null || num === undefined) return '0';
    return parseFloat(num).toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 2 });
}

function formatDateForInput(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// 使用公共工具函数
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '--';
    try {
        const date = new Date(dateTimeStr);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        return dateTimeStr;
    }
}

function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
    return null;
}

function addApiKeyHeader() {
    const apiKey = getCookie('api_key');
    return apiKey ? { 'X-API-Key': apiKey } : {};
}

function showApiKeyModal() {
    $('#apiKeyModal').removeClass('hidden');
}

function hideApiKeyModal() {
    $('#apiKeyModal').addClass('hidden');
}

function submitApiKey() {
    const apiKey = $('#apiKey').val().trim();
    if (!apiKey) {
        layer.msg('请输入访问密码', { icon: 2 });
        return;
    }

    // 设置cookie
    document.cookie = `api_key=${apiKey}; path=/; max-age=86400`;

    hideApiKeyModal();

    // 重新加载数据
    Promise.all([
        loadAnchorNames(),
        loadOrdersAndStats()
    ]).catch(error => {
        console.error('初始化数据加载失败:', error);
        layer.msg('加载数据失败: ' + error.message, { icon: 2 });
    });
} 