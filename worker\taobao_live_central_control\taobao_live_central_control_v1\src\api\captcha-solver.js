// 使用Node.js内置的fetch API (Node.js 18+)

/**
 * 滑块验证码解密处理器
 * 用于处理淘宝API返回FAIL_SYS_USER_VALIDATE错误时的滑块解密
 */
export class CaptchaSolver {
    constructor() {
        this.decryptServiceUrl = 'http://220.168.146.21:9107/tb_n';
        this.decryptKey = 'KnBx5v9p6Xpp8LBh6hnQPjlV1lR8iNmn9R5w3vKBqfnWyOPpXv4qHGgMrZOB8bD3BCOAvKghieLRS1L9ooBCpaVGq8k6Ij4XmYNLD2RWdB5owqpQRt2jVg96EdexOQWf';
        this.successCount = 0;
        this.failCount = 0;
    }

    /**
     * 检测是否需要进行滑块解密
     * @param {object} apiResponse - API响应结果
     * @returns {boolean}
     */
    needsCaptchaSolving(apiResponse) {
        if (!apiResponse || !apiResponse.error) {
            return false;
        }

        // 检查多种滑块验证错误类型
        return apiResponse.error.includes('FAIL_SYS_USER_VALIDATE') ||
               apiResponse.error.includes('RGV587_ERROR::SM') ||
               apiResponse.error.includes('哎哟喂,被挤爆啦');
    }

    /**
     * 从响应头中提取x5secdata
     * @param {Response} response - fetch响应对象
     * @returns {string|null}
     */
    extractX5SecData(response) {
        try {
            let setCookieHeaders = null;

            // 兼容不同版本的fetch API
            if (response.headers.raw && typeof response.headers.raw === 'function') {
                setCookieHeaders = response.headers.raw()['set-cookie'];
            } else if (response.headers.getSetCookie && typeof response.headers.getSetCookie === 'function') {
                setCookieHeaders = response.headers.getSetCookie();
            } else {
                const setCookieHeader = response.headers.get('set-cookie');
                if (setCookieHeader) {
                    setCookieHeaders = [setCookieHeader];
                }
            }

            if (!setCookieHeaders || setCookieHeaders.length === 0) {
                console.log('❌ 未找到set-cookie头');
                return null;
            }

            // 查找x5secdata
            for (const cookieHeader of setCookieHeaders) {
                if (cookieHeader.includes('x5secdata=')) {
                    const match = cookieHeader.match(/x5secdata=([^;]+)/);
                    if (match && match[1]) {
                        const x5secdata = match[1];
                        console.log('✅ 成功提取x5secdata');
                        return x5secdata;
                    }
                }
            }

            console.log('❌ 未在set-cookie中找到x5secdata');
            return null;

        } catch (error) {
            console.error('❌ 提取x5secdata失败:', error);
            return null;
        }
    }

    /**
     * 构建验证码URL
     * @param {string} originalUrl - 原始请求URL
     * @param {string} x5secdata - 提取的x5secdata值
     * @returns {string}
     */
    buildCaptchaUrl(originalUrl, x5secdata) {
        try {
            // 从原始URL中提取基础路径
            const url = new URL(originalUrl);
            const basePath = url.pathname;

            // 构建验证码URL
            const captchaUrl = `https://h5api.m.taobao.com${basePath}/_____tmd_____/punish?x5secdata=${encodeURIComponent(x5secdata)}&x5step=2&action=captcha&pureCaptcha=`;

            return captchaUrl;

        } catch (error) {
            console.error('❌ 构建验证码URL失败:', error);
            throw error;
        }
    }

    /**
     * 获取验证码配置信息
     * @param {string} captchaUrl - 验证码URL
     * @param {string} cookie - 请求cookie
     * @returns {Promise<object>}
     */
    async getCaptchaConfig(captchaUrl, cookie) {
        try {
            const headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Cookie': cookie
            };

            const response = await fetch(captchaUrl, {
                method: 'GET',
                headers: headers
            });

            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
            }

            const responseText = await response.text();

            // 提取配置信息
            const configMatch = responseText.match(/window\._config_\s*=\s*(\{.*?\});/s);
            if (!configMatch || !configMatch[1]) {
                console.error('❌ 未找到验证码配置信息');
                throw new Error('未找到验证码配置信息');
            }

            const configStr = configMatch[1];
            const config = JSON.parse(configStr);

            console.log('✅ 获取验证码配置成功');

            return config;

        } catch (error) {
            console.error('❌ 获取验证码配置失败:', error);
            throw error;
        }
    }

    /**
     * 调用解密服务
     * @param {object} captchaConfig - 验证码配置
     * @returns {Promise<object>}
     */
    async callDecryptService(captchaConfig) {
        try {
            if (captchaConfig.action !== 'captcha') {
                throw new Error(`不支持的验证码类型: ${captchaConfig.action}`);
            }

            const requestData = {
                captcha_config: captchaConfig,
                key: this.decryptKey
            };

            const response = await fetch(this.decryptServiceUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                throw new Error(`解密服务HTTP错误: ${response.status} ${response.statusText}`);
            }

            const responseText = await response.text();
            const result = JSON.parse(responseText);

            if (!result.urlEncode || !result.bx_et || !result.bx_pp || !result.referer) {
                throw new Error('解密服务返回数据不完整');
            }

            console.log('✅ 解密服务调用成功');
            return result;

        } catch (error) {
            console.error('❌ 调用解密服务失败:', error);
            throw error;
        }
    }

    /**
     * 获取x5sec值
     * @param {string} decryptUrl - 解密URL
     * @param {object} decryptHeaders - 解密请求头
     * @param {string} cookie - 原始cookie
     * @returns {Promise<string>}
     */
    async getX5SecValue(decryptUrl, decryptHeaders, cookie) {
        try {
            const headers = {
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'bx-v': '2.5.3',
                'Cache-Control': 'no-cache',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Pragma': 'no-cache',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"Windows"',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Cookie': cookie,
                ...decryptHeaders
            };

            const response = await fetch(decryptUrl, {
                method: 'GET',
                headers: headers
            });

            if (!response.ok) {
                const responseText = await response.text();
                throw new Error(`获取x5sec HTTP错误: ${response.status} ${response.statusText} - ${responseText}`);
            }

            // 检查响应中是否包含x5sec cookie
            let setCookieHeaders = null;
            if (response.headers.raw && typeof response.headers.raw === 'function') {
                setCookieHeaders = response.headers.raw()['set-cookie'];
            } else if (response.headers.getSetCookie && typeof response.headers.getSetCookie === 'function') {
                setCookieHeaders = response.headers.getSetCookie();
            } else {
                const setCookieHeader = response.headers.get('set-cookie');
                if (setCookieHeader) {
                    setCookieHeaders = [setCookieHeader];
                }
            }

            if (setCookieHeaders && setCookieHeaders.length > 0) {
                for (const cookieHeader of setCookieHeaders) {
                    if (cookieHeader.includes('x5sec=')) {
                        const match = cookieHeader.match(/x5sec=([^;]+)/);
                        if (match && match[1]) {
                            const x5secValue = match[1];
                            console.log('✅ 成功获取x5sec值:', x5secValue.substring(0, 50) + '...');
                            this.successCount++;
                            return x5secValue;
                        }
                    }
                }
            }

            console.log('❌ 未在响应中找到x5sec值');
            this.failCount++;
            throw new Error('未找到x5sec值');

        } catch (error) {
            console.error('❌ 获取x5sec值失败:', error);
            this.failCount++;
            throw error;
        }
    }

    /**
     * 更新Cookie中的x5sec值
     * @param {string} originalCookie - 原始cookie
     * @param {string} x5secValue - 新的x5sec值
     * @returns {string}
     */
    updateCookieWithX5Sec(originalCookie, x5secValue) {
        try {
            // 解析现有cookie
            const cookieMap = {};
            if (originalCookie) {
                originalCookie.split(';').forEach(cookie => {
                    const [name, value] = cookie.trim().split('=');
                    if (name && value) {
                        cookieMap[name] = value;
                    }
                });
            }

            // 更新或添加x5sec
            const oldX5sec = cookieMap['x5sec'];
            cookieMap['x5sec'] = x5secValue;

            // 重新组装cookie
            const updatedCookie = Object.keys(cookieMap)
                .map(name => `${name}=${cookieMap[name]}`)
                .join('; ');

            console.log(`🍪 Cookie已更新 (${oldX5sec ? '更新' : '添加'}x5sec)`);
            return updatedCookie;

        } catch (error) {
            console.error('❌ 更新Cookie失败:', error);
            throw error;
        }
    }

    /**
     * 完整的滑块解密流程
     * @param {object} apiResponse - 失败的API响应
     * @param {string} originalUrl - 原始请求URL
     * @param {string} originalCookie - 原始cookie
     * @returns {Promise<string>} - 更新后的cookie
     */
    async solveCaptcha(apiResponse, originalUrl, originalCookie) {
        try {
            console.log('🔓 开始滑块解密流程...');

            // 步骤1: 检查是否需要解密
            if (!this.needsCaptchaSolving(apiResponse)) {
                throw new Error('不需要进行滑块解密');
            }

            // 步骤2: 提取x5secdata
            const x5secdata = this.extractX5SecData(apiResponse.response);
            if (!x5secdata) {
                throw new Error('无法提取x5secdata');
            }

            // 步骤3: 构建验证码URL
            const captchaUrl = this.buildCaptchaUrl(originalUrl, x5secdata);

            // 步骤4: 获取验证码配置
            const captchaConfig = await this.getCaptchaConfig(captchaUrl, originalCookie);

            // 步骤5: 调用解密服务
            const decryptResult = await this.callDecryptService(captchaConfig);

            // 步骤6: 获取x5sec值
            const x5secValue = await this.getX5SecValue(
                decryptResult.urlEncode,
                {
                    'bx_et': decryptResult.bx_et,
                    'bx-pp': decryptResult.bx_pp,
                    'referer': decryptResult.referer
                },
                originalCookie
            );

            // 步骤7: 更新cookie
            const updatedCookie = this.updateCookieWithX5Sec(originalCookie, x5secValue);

            // 打印统计信息
            const stats = this.getStats();
            console.log(`📊 滑块解密成功率: ${stats.successRate}% (${stats.successCount}/${stats.totalAttempts})`);

            return updatedCookie;

        } catch (error) {
            console.error('❌ 滑块解密失败:', error);
            throw error;
        }
    }

    /**
     * 获取统计信息
     * @returns {object}
     */
    getStats() {
        const totalAttempts = this.successCount + this.failCount;
        const successRate = totalAttempts > 0 ? ((this.successCount / totalAttempts) * 100).toFixed(2) : 0;

        return {
            successCount: this.successCount,
            failCount: this.failCount,
            totalAttempts,
            successRate: parseFloat(successRate)
        };
    }

    /**
     * 重置统计信息
     */
    resetStats() {
        this.successCount = 0;
        this.failCount = 0;
        console.log('📊 统计信息已重置');
    }
}
