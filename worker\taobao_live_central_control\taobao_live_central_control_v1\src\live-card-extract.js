import crypto from 'crypto';
import { handleTaobaoJsonpResponse } from './utils/response-handler.js';
import prohibitedWordsFilter from './prohibited-words-filter.js';

/**
 * 淘宝直播产品手卡提取工具
 */
class LiveCardExtractor {
    constructor() {
        this.appKey = '12574478';
        this.jsv = '2.7.4';
        this.apiName = 'mtop.tblive.portal.item.card.user.token';
        this.apiVersion = '1.0';
    }

    /**
     * MD5哈希函数
     * @param {string} string 
     * @returns {string}
     */
    md5(string) {
        return crypto.createHash('md5').update(string, 'utf8').digest('hex');
    }

    /**
     * 获取用户token（reqKey）
     * @param {string} liveId
     * @param {string} h5Token
     * @param {string} cookie
     * @param {string} anchorName - 主播名称，用于cookie更新
     * @returns {Promise<string|null>}
     */
    async getUserToken(liveId, h5Token, cookie, anchorName) {
        console.log(`🔐 开始获取用户token - liveId: ${liveId}`);

        const timestamp = Date.now().toString();
        const dataStr = JSON.stringify({ liveId });
        const signString = `${h5Token}&${timestamp}&${this.appKey}&${dataStr}`;
        const sign = this.md5(signString);

        console.log(`📝 签名参数 - timestamp: ${timestamp}, dataStr: ${dataStr}`);
        console.log(`🔑 签名字符串: ${signString}`);
        console.log(`✍️ 签名结果: ${sign}`);

        const params = new URLSearchParams({
            jsv: this.jsv,
            appKey: this.appKey,
            t: timestamp,
            sign: sign,
            api: this.apiName,
            v: this.apiVersion,
            preventFallback: 'true',
            type: 'jsonp',
            dataType: 'jsonp',
            callback: 'mtopjsonp189',
            data: dataStr
        });

        const url = `https://h5api.m.taobao.com/h5/${this.apiName}/${this.apiVersion}/?${params}`;
        console.log(`🌐 请求URL: ${url}`);

        const headers = {
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Referer': 'https://liveplatform.taobao.com/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Cookie': cookie
        };

        try {
            console.log(`📡 发送请求...`);
            const response = await fetch(url, { method: 'GET', headers });
            console.log(`📊 响应状态: ${response.status}`);

            const text = await response.text();
            console.log(`📄 响应内容: ${text.substring(0, 500)}...`);

            // 使用统一的JSONP响应处理
            const responseResult = await handleTaobaoJsonpResponse(text, anchorName, response, cookie);

            if (responseResult.success) {
                console.log(`✅ 获取token成功: ${responseResult.data ? responseResult.data.substring(0, 20) + '...' : 'null'}`);
                return responseResult.data;
            } else {
                console.log(`❌ API返回失败: ${responseResult.error}`);

                // 检查是否为令牌过期错误
                if (responseResult.errorType === 'TOKEN_EXPIRED') {
                    const tokenExpiredError = new Error('令牌过期，停止任务执行');
                    tokenExpiredError.isTokenExpired = true;
                    throw tokenExpiredError;
                } else if (responseResult.errorType === 'TOKEN_EMPTY_BUT_COOKIE_UPDATED') {
                    console.log(`🍪 检测到令牌为空但cookie已更新，主播: ${anchorName}`);
                    // 可以选择重试或返回null
                    return null;
                } else if (responseResult.needsCaptchaSolving) {
                    console.log(`🔓 检测到滑块验证错误，手卡提取暂不支持自动解密，主播: ${anchorName}`);
                    const captchaError = new Error('需要滑块验证，请稍后重试');
                    captchaError.needsCaptcha = true;
                    throw captchaError;
                }
            }
        } catch (error) {
            console.log(`💥 请求异常:`, error.message);
            // 如果是令牌过期错误，需要重新抛出
            if (error.isTokenExpired) {
                throw error;
            }
        }
        return null;
    }

    /**
     * 获取手卡内容
     * @param {string} liveId
     * @param {string} itemId
     * @param {string} reqKey
     * @param {string} cookie
     * @param {object} extractParams - 提取参数
     * @returns {Promise<object|null>} { script, ... }
     */
    async getCardInfo(liveId, itemId, reqKey, cookie, extractParams = null) {
        // 默认参数
        let type = '1';
        let subType = '1';
        let isRenew = 'false';
        let scene = 'normal';
        
        // 如果传递了参数，使用传递的参数
        if (extractParams) {
            type = extractParams.type || type;
            subType = extractParams.subType || subType;
            isRenew = extractParams.isRenew !== undefined ? extractParams.isRenew.toString() : isRenew;
            scene = extractParams.scene || scene;
            
            console.log(`🔧 使用参数 - itemId: ${itemId}, type: ${type}, subType: ${subType}, isRenew: ${isRenew}, scene: ${scene}`);
        }

        const params = new URLSearchParams({
            itemId,
            liveId,
            type,
            subType: subType.toString(),
            isRenew,
            scene,
            reqKey,
            sceneCode: 'beforeLivebatch',
            trackId: Math.random().toString(36).substring(2, 11),
            isFilterSavedScript: 'true'
        });
        const url = `https://tblive.taobao.com/api/sse/script/query?${params}`;

        const headers = {
            'Accept': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Referer': 'https://liveplatform.taobao.com/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Cookie': cookie
        };

        try {
            console.log(`🌐 发送手卡请求 - itemId: ${itemId}, URL: ${url}`);
            const response = await fetch(url, { method: 'GET', headers });

            if (!response.ok) {
                console.log(`❌ 手卡请求失败 - itemId: ${itemId}, 状态: ${response.status}`);
                return null;
            }

            console.log(`📡 开始解析SSE流 - itemId: ${itemId}`);
            // 解析SSE流
            const result = await this.parseSSEStream(response.body, itemId);
            console.log(`🎯 SSE流解析完成 - itemId: ${itemId}, 结果:`, result ? '成功' : '失败');
            if (!result) {
                console.log(`❌ SSE流解析失败 - itemId: ${itemId}, 可能原因: 1.网络连接问题 2.服务器响应格式异常 3.该产品确实没有手卡内容`);
            }
            return result;
        } catch (error) {
            console.log(`💥 手卡请求异常 - itemId: ${itemId}, 错误:`, error.message);
            return null;
        }
    }

    /**
     * 解析SSE流，拼接script
     * @param {ReadableStream} stream
     * @param {string} itemId
     * @returns {Promise<object|null>}
     */
    async parseSSEStream(stream, itemId) {
        const reader = stream.getReader();
        const decoder = new TextDecoder();
        let fullScript = '';
        let totalData = '';
        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;
                const chunk = decoder.decode(value, { stream: true });
                totalData += chunk;
                const lines = chunk.split('\n');
                for (let line of lines) {
                    line = line.trim();
                    if (!line) continue;
                    // 1. data: 格式
                    if (line.startsWith('data:')) {
                        let content = line.startsWith('data: ')
                            ? line.substring(6)
                            : line.substring(5);
                        if (!content) continue;
                        try {
                            const jsonData = JSON.parse(content);
                            const useCache = jsonData.useCache || false;
                            const finished = jsonData.finished || false;
                            const script = jsonData.script || '';
                            if (script) {
                                fullScript = script;
                                if (useCache || finished) {
                                    return { itemId, script: fullScript };
                                }
                            }
                        } catch (e) {}
                    // 2. 直接json格式
                    } else if (line.startsWith('{')) {
                        try {
                            const jsonData = JSON.parse(line);
                            const useCache = jsonData.useCache || false;
                            const finished = jsonData.finished || false;
                            const script = jsonData.script || '';
                            if (script) {
                                fullScript = script;
                                if (useCache || finished) {
                                    return { itemId, script: fullScript };
                                }
                            }
                        } catch (e) {}
                    }
                }
            }
            if (fullScript) {
                return { itemId, script: fullScript };
            }
        } finally {
            reader.releaseLock();
        }
        
        // 如果没有找到script，输出完整响应数据用于调试
        console.log(`❌ 未找到手卡内容 - itemId: ${itemId}`);
        console.log(`📄 完整SSE响应数据:`, totalData.substring(0, 1000) + (totalData.length > 1000 ? '...' : ''));
        
        return null;
    }

    /**
     * 总入口：提取手卡
     * @param {string} liveId
     * @param {string} itemId
     * @param {string} h5Token
     * @param {string} cookie
     * @param {string} anchorName - 主播名称，用于cookie更新
     * @param {object} extractParams - 提取参数
     * @returns {Promise<object|null>}
     */
    async extractCard(liveId, itemId, h5Token, cookie, anchorName, extractParams = null) {
        console.log(`🔍 开始提取手卡 - liveId: ${liveId}, itemId: ${itemId}`);

        try {
            const reqKey = await this.getUserToken(liveId, h5Token, cookie, anchorName);
            if (!reqKey) {
                console.log(`❌ 获取reqKey失败 - liveId: ${liveId}, itemId: ${itemId}`);
                throw new Error('获取手卡token失败');
            }

            console.log(`🔑 获取reqKey成功 - liveId: ${liveId}, itemId: ${itemId}, reqKey: ${reqKey.substring(0, 10)}...`);

            const result = await this.getCardInfo(liveId, itemId, reqKey, cookie, extractParams);
            console.log(`📝 手卡提取完成 - liveId: ${liveId}, itemId: ${itemId}, 结果:`, result ? '成功' : '失败');

            // 如果勾选了过滤违禁词且获取到了手卡内容，进行违禁词过滤
            if (result && result.script && extractParams && extractParams.filterProhibitedWords) {
                console.log(`🔍 开始过滤违禁词 - itemId: ${itemId}, 原始内容长度: ${result.script.length}`);

                try {
                    const filterResult = await prohibitedWordsFilter.filterHandCardContent(result.script);

                    if (filterResult.hasProhibitedWords) {
                        console.log(`🚫 发现违禁词 - itemId: ${itemId}, 违禁词: [${filterResult.foundWords.join(', ')}], 过滤后长度: ${filterResult.cleanedContent.length}`);

                        // 更新手卡内容为过滤后的内容
                        result.script = filterResult.cleanedContent;

                        // 添加过滤信息到结果中
                        result.filteredWords = filterResult.foundWords;
                        result.isFiltered = true;
                    } else {
                        console.log(`✅ 未发现违禁词 - itemId: ${itemId}`);
                        result.isFiltered = false;
                    }
                } catch (filterError) {
                    console.error(`❌ 违禁词过滤失败 - itemId: ${itemId}, 错误:`, filterError.message);
                    // 过滤失败不影响手卡提取，继续返回原始内容
                    result.filterError = filterError.message;
                    result.isFiltered = false;
                }
            }

            return result;
        } catch (error) {
            // 如果是令牌过期错误，直接重新抛出
            if (error.isTokenExpired) {
                throw error;
            }
            // 其他错误也重新抛出，保持原有行为
            throw error;
        }
    }
}

export default LiveCardExtractor; 