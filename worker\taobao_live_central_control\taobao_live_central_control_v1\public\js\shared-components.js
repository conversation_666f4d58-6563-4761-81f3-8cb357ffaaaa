/**
 * 共享组件JavaScript文件
 * 包含通用的按钮事件处理和组件逻辑
 */

/**
 * 绑定主播按钮点击事件
 * @param {Function} onAnchorChange 主播改变时的回调函数，接收anchorName参数
 */
function bindAnchorButtonEvents(onAnchorChange) {
    $('.anchor-btn').off('click').on('click', function() {
        const anchorName = $(this).data('anchor');

        // 更新按钮状态
        $('.anchor-btn').removeClass('anchor-btn-active');
        $(this).addClass('anchor-btn-active');

        // 更新隐藏的筛选字段
        $('#anchorFilter').val(anchorName);

        // 执行回调函数
        if (typeof onAnchorChange === 'function') {
            onAnchorChange(anchorName);
        }

        console.log('选择主播:', anchorName || '全部');
    });
}

/**
 * 绑定时间按钮点击事件
 * @param {Function} onTimeChange 时间改变时的回调函数，接收days参数
 */
function bindTimeButtonEvents(onTimeChange) {
    $('.time-btn').off('click').on('click', function() {
        const days = $(this).data('days');

        // 更新按钮状态
        $('.time-btn').removeClass('time-btn-active');
        $(this).addClass('time-btn-active');

        // 更新隐藏的筛选字段
        $('#timeFilter').val(days);

        // 执行回调函数
        if (typeof onTimeChange === 'function') {
            onTimeChange(days);
        }

        console.log('选择时间范围:', days ? `${days}天内` : '全部');
    });
}

/**
 * 生成主播按钮HTML
 * @param {Array} anchors 主播列表
 * @param {string} containerSelector 容器选择器
 * @param {Function} onAnchorChange 主播改变时的回调函数
 */
function generateAnchorButtons(anchors, containerSelector, onAnchorChange) {
    const container = $(containerSelector);
    
    // 清空容器，但保留"全部"按钮
    container.find('.anchor-btn:not([data-anchor=""])').remove();

    if (anchors && anchors.length > 0) {
        // 按sort字段正序排列主播
        const sortedAnchors = [...anchors].sort((a, b) => {
            const sortA = parseInt(a.sort) || 0;
            const sortB = parseInt(b.sort) || 0;
            return sortA - sortB;
        });

        // 添加主播按钮
        sortedAnchors.forEach(anchor => {
            const button = $(`
                <button type="button" class="anchor-btn" data-anchor="${anchor.anchor_name}">
                    ${anchor.anchor_name}
                </button>
            `);
            container.append(button);
        });

        // 重新绑定事件
        bindAnchorButtonEvents(onAnchorChange);
    }
}

/**
 * 重置筛选按钮状态
 */
function resetFilterButtons() {
    // 重置主播按钮状态
    $('.anchor-btn').removeClass('anchor-btn-active');
    $('.anchor-btn[data-anchor=""]').addClass('anchor-btn-active');
    $('#anchorFilter').val('');

    // 重置时间按钮状态
    $('.time-btn').removeClass('time-btn-active');
    $('.time-btn[data-days=""]').addClass('time-btn-active');
    $('#timeFilter').val('');
}

/**
 * 加载主播列表并生成按钮
 * @param {string} containerSelector 容器选择器
 * @param {Function} onAnchorChange 主播改变时的回调函数
 */
async function loadAndGenerateAnchorButtons(containerSelector, onAnchorChange) {
    try {
        const response = await fetch('/api/anchors?mode=full', {
            headers: addApiKeyHeader()
        });

        if (response.status === 401) {
            const data = await response.json();
            if (data.error === "invalid_api_key") {
                showApiKeyModal();
                return;
            }
        }

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || '获取主播列表失败');
        }

        generateAnchorButtons(data.anchors, containerSelector, onAnchorChange);

    } catch (error) {
        console.error('加载主播列表失败:', error);
        if (typeof layer !== 'undefined') {
            layer.msg('加载主播列表失败: ' + error.message, { icon: 2 });
        } else {
            alert('加载主播列表失败: ' + error.message);
        }
    }
}

// 导出函数供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        bindAnchorButtonEvents,
        bindTimeButtonEvents,
        generateAnchorButtons,
        resetFilterButtons,
        loadAndGenerateAnchorButtons
    };
}
