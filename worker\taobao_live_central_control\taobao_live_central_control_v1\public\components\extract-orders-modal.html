<!-- 提取订单弹窗 -->
<div id="extractOrdersModal" class="fixed z-50 inset-0 overflow-y-auto hidden">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">

            <!-- 弹窗头部 -->
            <div class="bg-white px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">
                        提取订单数据
                    </h3>
                    <button type="button" onclick="hideExtractOrdersModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>

            <!-- 弹窗内容 -->
            <div class="bg-white px-6 py-4">
                <!-- 时间段选择 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">选择时间段</label>

                    <!-- 开始时间 -->
                    <div class="mb-3">
                        <label class="block text-xs font-medium text-gray-600 mb-1">开始时间</label>
                        <div class="flex gap-2">
                            <input type="date" id="orderStartDate" class="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500">
                            <input type="time" id="orderStartTime" value="00:00" class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500">
                        </div>
                    </div>

                    <!-- 结束时间 -->
                    <div class="mb-4">
                        <label class="block text-xs font-medium text-gray-600 mb-1">结束时间</label>
                        <div class="flex gap-2">
                            <input type="date" id="orderEndDate" class="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500">
                            <input type="time" id="orderEndTime" value="23:59" class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500">
                        </div>
                    </div>
                </div>

                <!-- 快捷选择 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">快捷选择</label>
                    <div class="grid grid-cols-2 gap-2">
                        <button type="button" class="quick-select-btn" data-days="3">
                            <i class="fas fa-calendar-alt mr-2"></i>3天
                        </button>
                        <button type="button" class="quick-select-btn" data-days="7">
                            <i class="fas fa-calendar-week mr-2"></i>7天
                        </button>
                        <button type="button" class="quick-select-btn" data-days="15">
                            <i class="fas fa-calendar mr-2"></i>15天
                        </button>
                        <button type="button" class="quick-select-btn" data-days="30">
                            <i class="fas fa-calendar-check mr-2"></i>30天
                        </button>
                    </div>
                </div>

                <!-- 主播选择 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">选择主播</label>
                    <div id="orderAnchorButtons" class="grid grid-cols-3 gap-2">
                        <button type="button" class="order-anchor-btn order-anchor-btn-active" data-anchor="">
                            全部主播
                        </button>
                        <!-- 主播按钮将通过JavaScript动态生成 -->
                    </div>
                    <input type="hidden" id="orderAnchorSelect" value="">
                </div>
            </div>

            <!-- 弹窗底部 -->
            <div class="bg-gray-50 px-6 py-4 flex justify-end space-x-3">
                <button type="button" onclick="hideExtractOrdersModal()"
                    class="inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50">
                    取消
                </button>
                <button type="button" id="confirmExtractOrders"
                    class="inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-emerald-600 text-base font-medium text-white hover:bg-emerald-700">
                    提取订单
                </button>
            </div>
        </div>
    </div>
</div>
